// ways to narrow types
// union type is a type that can be one of several types
function isNumber(value: unknown): boolean {
  return typeof value === "number";
}

function processValue(value: unknown): number | string | boolean | null {
  if (typeof value === "number") {
    return value * 2;
  } else if (typeof value === "string") {
    return value.toUpperCase();
  } else if (typeof value === "boolean") {
    return !value;
  }
  return null;
}

function printIfTruthy(value: unknown) {
  if (value) {
    console.log("Truthy");
  } else {
    console.log("Falsy");
  }
}

function isNonEmptyArray(value: unknown) {
  if (Array.isArray(value)) {
    return value ? true : false;
  }
  return false;
}

interface Circle {
  radius: number;
  type: "circle";
}

interface Rectangle {
  width: number;
  height: number;
  type: "rectangle";
}
type Shape = Circle | Rectangle;

function getArea(shape: Shape) {
  if ("radius" in shape) {
    return 3.14 * shape.radius * shape.radius;
  }
  return shape.width * shape.height;
}
