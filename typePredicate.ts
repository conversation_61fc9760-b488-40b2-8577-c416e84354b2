function isNumber(value: unknown): value is number {
  return typeof value === "number";
}

const value: unknown = 42;

function logValuePlus1(value: unknown) {
  if (isNumber(value)) {
    return value + 1;
  }
  return value;
}

function isArray(value: unknown): value is Array<any> {
  return Array.isArray(value);
}

const arrayExample: unknown = [1, 2, 3];

function logFirstElement(value: unknown) {
  if (isArray(value)) return value[0];
  return value;
}

logValuePlus1(value);
logFirstElement(arrayExample);
